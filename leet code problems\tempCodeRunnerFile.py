import collections
import heapq
class Twitter(object):

    def __init__(self):
        self.timestamp=0
        self.following=collections.defaultdict(set)
        self.tweets=collections.defaultdict(collections.deque)
    def postTweet(self, userId, tweetId):
        """
        :type userId: int
        :type tweetId: int
        :rtype: None
        """
        self.timestamp += 1
        self.tweets[userId].appendleft((self.timestamp, tweetId))
        # Ensure user follows themselves
        self.following[userId].add(userId)

    def getNewsFeed(self, userId):
        """
        :type userId: int
        :rtype: List[int]
        """
        # Ensure user follows themselves
        self.following[userId].add(userId)

        news_feed_heap = []
        for followee_id in self.following[userId]:
            for tweet_timestamp, tweet_id in self.tweets[followee_id]:
                heapq.heappush(news_feed_heap, (-tweet_timestamp, tweet_id))

        final_feed = []
        while news_feed_heap and len(final_feed) < 10:
            neg_ts, tweet_id = heapq.heappop(news_feed_heap)
            final_feed.append(tweet_id)
        return final_feed
    def follow(self, followerId, followeeId):
        """
        :type followerId: int
        :type followeeId: int
        :rtype: None
        """
        self.following[followerId].add(followeeId)
        

    def unfollow(self, followerId, followeeId):
        """
        :type followerId: int
        :type followeeId: int
        :rtype: None
        """
        if followerId != followeeId and followeeId in self.following[followerId]:
            self.following[followerId].remove(followeeId)

twitter=Twitter()
print(twitter.postTweet(1, 5))
print(twitter.getNewsFeed(1)) 
print(twitter.follow(1, 2))  
print(twitter.postTweet(2, 6))
print(twitter.getNewsFeed(1)) 
print(twitter.unfollow(1, 2)) 
print(twitter.getNewsFeed(1))