# K largest element in a stream
import heapq
class KthLargest(object):

    def __init__(self, k, nums):
        """
        :type k: int
        :type nums: List[int]
        """
        self.k = k
        self.heap = nums
        heapq.heapify(self.heap)
        # Keep only k largest elements (min-heap of size k)
        while len(self.heap) > k:
            heapq.heappop(self.heap)

    def add(self, val):
        """
        :type val: int
        :rtype: int
        """
        # Add the new value to heap
        heapq.heappush(self.heap, val)

        # If heap size exceeds k, remove the smallest element
        if len(self.heap) > self.k:
            heapq.heappop(self.heap)

        return self.heap[0]

kthLargest = KthLargest(3, [4, 5, 8, 2])
print(kthLargest.add(3))   
print(kthLargest.add(5))   
print(kthLargest.add(10))  
print(kthLargest.add(9))   
print(kthLargest.add(4))   

# Relative ranks
class Solution(object):
    def findRelativeRanks(self, score):
        """
        :type score: List[int]
        :rtype: List[str]
        """
        n = len(score)
        ans = [""] * n

        heap = []
        for pos, ath_score in enumerate(score):
            heap.append((-ath_score, pos))

        heapq.heapify(heap)

        for rank in range(1, n + 1):
            neg_score, pos = heapq.heappop(heap)   # get highest score
            if rank == 1:
                ans[pos] = "Gold Medal"
            elif rank == 2:
                ans[pos] = "Silver Medal"
            elif rank == 3:
                ans[pos] = "Bronze Medal"
            else:
                ans[pos] = str(rank)

        return ans

s = Solution()
print(s.findRelativeRanks([5, 4, 3, 2, 1]))
       
# Last stone weight
import heapq
class Solution(object):
    def lastStoneWeight(self, stones):
        """
        :type stones: List[int]
        :rtype: int
        """
        # Convert to negative values to simulate max-heap with min-heap
        heap = [-stone for stone in stones]
        heapq.heapify(heap)

        while len(heap) > 1:
            # Get two heaviest stones (most negative values)
            first = -heapq.heappop(heap)   # Convert back to positive
            second = -heapq.heappop(heap)  # Convert back to positive

            # If stones have different weights, push the difference back
            if first != second:
                heapq.heappush(heap, -(first - second))

        # Return the last stone weight, or 0 if no stones left
        return -heap[0] if heap else 0

s = Solution()
print(s.lastStoneWeight([2, 7, 4, 1, 8, 1]))

# The kth weakest row in a matrix
class Solution(object):
    def kWeakestRows(self, mat, k):
        """
        :type mat: List[List[int]]
        :type k: int
        :rtype: List[int]
        """
        r=len(mat)
        r_strength=[]
        for i in range(r):
            so_count=sum(mat[i])
            r_strength.append((so_count,i))

        heapq.heapify(r_strength)
    
        result=[]
        for i in range(k):
            so_count,k=heapq.heappop(r_strength)
            result.append(k)
        return result
    
s=Solution()
print(s.kWeakestRows([[1,1,0,0,0],[1,1,1,1,0],[1,0,0,0,0],[1,1,0,0,0],[1,1,1,1,1]],3))

# Maximum product of two elements
class Solution(object):
    def maxProduct(self, nums):
        """
        :type nums: List[int]
        :rtype: int
        """
        # Create max-heap using negative values
        max_heap = [-num for num in nums]
        heapq.heapify(max_heap)

        # Get two largest elements
        first_max = -heapq.heappop(max_heap)   # Convert back to positive
        second_max = -heapq.heappop(max_heap)  # Convert back to positive

        # Calculate (first_max - 1) * (second_max - 1)
        max_val = (first_max - 1) * (second_max - 1)
        return max_val
    
s=Solution()
print(s.maxProduct([3,4,5,2]))

# Kth largest elemnet in an array
class Solution(object):
    def findKthLargest(self, nums, k):
        """
        :type nums: List[int]
        :type k: int
        :rtype: int
        """
        max_heap = [-num for num in nums]
        heapq.heapify(max_heap)
        for i in range(k):
            k_largest_element=-heapq.heappop(max_heap)
        return k_largest_element
    
s=Solution()
print(s.findKthLargest([3,2,1,5,6,4],2))

# Design Twitter
import collections
import heapq
class Twitter(object):

    def __init__(self):
        self.timestamp=0
        self.following=collections.defaultdict(set)
        self.tweets=collections.defaultdict(collections.deque)
    def postTweet(self, userId, tweetId):
        """
        :type userId: int
        :type tweetId: int
        :rtype: None
        """
        self.timestamp += 1
        self.tweets[userId].appendleft((self.timestamp, tweetId))
        # Ensure user follows themselves
        self.following[userId].add(userId)
        return None

    def getNewsFeed(self, userId):
        """
        :type userId: int
        :rtype: List[int]
        """
        # Ensure user follows themselves
        self.following[userId].add(userId)

        news_feed_heap = []
        for followee_id in self.following[userId]:
            for tweet_timestamp, tweet_id in self.tweets[followee_id]:
                heapq.heappush(news_feed_heap, (-tweet_timestamp, tweet_id))

        final_feed = []
        while news_feed_heap and len(final_feed) < 10:
            neg_ts, tweet_id = heapq.heappop(news_feed_heap)
            final_feed.append(tweet_id)
        return final_feed
    def follow(self, followerId, followeeId):
        """
        :type followerId: int
        :type followeeId: int
        :rtype: None
        """
        self.following[followerId].add(followeeId)
        return None
        

    def unfollow(self, followerId, followeeId):
        """
        :type followerId: int
        :type followeeId: int
        :rtype: None
        """
        if followerId != followeeId and followeeId in self.following[followerId]:
            self.following[followerId].remove(followeeId)
        return None

# Test case that matches the expected output
twitter = Twitter()
print(twitter.postTweet(1, 5))      # None
print(twitter.getNewsFeed(1))       # [5]
print(twitter.follow(1, 2))         # None
print(twitter.postTweet(2, 6))      # None
print(twitter.getNewsFeed(1))       # [6, 5]
print(twitter.unfollow(1, 2))       # None
print(twitter.getNewsFeed(1))       # [5]

# Test to match the expected pattern: [[],[1,5],[1,2],[2,1],[2],[2,6],[1],[2],[2,1],[1],[2],[1,2],[1],[2]]
print("--- Extended Test ---")
twitter2 = Twitter()
operations = ["Twitter", "postTweet", "follow", "postTweet", "getNewsFeed", "postTweet", "getNewsFeed", "getNewsFeed", "unfollow", "getNewsFeed", "getNewsFeed", "follow", "getNewsFeed", "getNewsFeed"]
params = [[], [1,5], [1,2], [2,1], [2], [2,6], [1], [2], [2,1], [1], [2], [1,2], [1], [2]]

results = []
for i, (op, param) in enumerate(zip(operations, params)):
    if op == "Twitter":
        twitter2 = Twitter()
        results.append([])  # Empty array instead of None for constructor
    elif op == "postTweet":
        result = twitter2.postTweet(param[0], param[1])
        results.append([])  # Empty array instead of None for void methods
    elif op == "follow":
        result = twitter2.follow(param[0], param[1])
        results.append([])  # Empty array instead of None for void methods
    elif op == "unfollow":
        result = twitter2.unfollow(param[0], param[1])
        results.append([])  # Empty array instead of None for void methods
    elif op == "getNewsFeed":
        result = twitter2.getNewsFeed(param[0])
        results.append(result)

print(results)

# Nth ugly number
class Solution(object):
    def nthUglyNumber(self, n):
        """
        :type n: int
        :rtype: int
        """
        if n == 1:
            return 1

        # Use three pointers for multiples of 2, 3, and 5
        ugly = [0] * n
        ugly[0] = 1

        i2 = i3 = i5 = 0

        for i in range(1, n):
            # Calculate next candidates
            next_2 = ugly[i2] * 2
            next_3 = ugly[i3] * 3
            next_5 = ugly[i5] * 5

            # Choose the minimum
            next_ugly = min(next_2, next_3, next_5)
            ugly[i] = next_ugly

            # Move pointers forward if they generated the chosen number
            if next_ugly == next_2:
                i2 += 1
            if next_ugly == next_3:
                i3 += 1
            if next_ugly == next_5:
                i5 += 1

        return ugly[n-1]

s=Solution()
print(s.nthUglyNumber(10))  # Should print 12
print(s.nthUglyNumber(2))   # Should print 2
