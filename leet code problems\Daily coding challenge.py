class Solution(object):
    def maxBottlesDrunk(self, numBottles, numExchange):
        """
        :type numBottles: int
        :type numExchange: int
        :rtype: int
        """
        empty_bottles=0
        bottles_drunk=0

        empty_bottles += numBottles
        bottles_drunk += numBottles

        while empty_bottles >= numExchange:
            empty_bottles -= numExchange
            numExchange += 1
            bottles_drunk += 1
            empty_bottles += 1
        
        return bottles_drunk
     
s=Solution()
print(s.maxBottlesDrunk(10,3))
print(s.maxBottlesDrunk(13,6))

# Container with most water
class Solution(object):
    def maxArea(self, height):
        """
        :type height: List[int]
        :rtype: int
        """
        n=len(height)
        l =0
        r= n - 1
        maxA=0
        if n < 2: return
        while(l<r):
            minH=min(height[l],height[r])
            A=minH * (r-l)
            maxA=max(maxA,A)
            if height[l] < height[r]:
                l += 1
            else:
                r -= 1
        return maxA

s=Solution()
print(s.maxArea([1,8,6,2,5,4,8,3,7]))